"use client";

import { Vote, Users, LinkIcon, Calendar } from "lucide-react"
import Link from "next/link";

import {
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from "@/shared/ui/sidebar"
import {cn} from "@/shared/lib";
import {usePathname} from "next/navigation";

// Menu items.
const items = [
    {
        title: "Ovozlar",
        url: "/dashboard",
        icon: <Vote />,
    },
    {
        title: "Xo'jaliklar",
        url: "/dashboard/village-people",
        icon: <Users />,
    },
    {
        title: "Ovozlarni biriktirish",
        url: "/dashboard/connect-votes",
        icon: <LinkIcon />,
    },
    {
        title: "Kunlar bo'yicha ovozlar",
        url: "/dashboard/daily-votes",
        icon: <Calendar />,
    },
]

export function AppSidebar() {
    const pathname = usePathname()
    return (
        <SidebarGroup>
            <SidebarGroupLabel>{"Ko'ktol ovozlar boshqaruv paneli"}</SidebarGroupLabel>
            <SidebarGroupContent>
                <SidebarMenu>
                    <>
                        {items.map((item) => (
                            <SidebarMenuItem
                              key={item.title}
                              className={cn({
                                  'bg-sidebar-accent': item.url === pathname,
                              })}
                            >
                                <SidebarMenuButton asChild>
                                    <Link href={item.url}>
                                        {item.icon}
                                        <span>{item.title}</span>
                                    </Link>
                                </SidebarMenuButton>
                            </SidebarMenuItem>
                        ))}
                    </>
                </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup>
    )
}