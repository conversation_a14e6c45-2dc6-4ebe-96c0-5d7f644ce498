"use client"

import { useDailyVotes } from "@/entity/votes/model/use-daily-votes"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/shared/ui/card"
import { Badge } from "@/shared/ui/badge"
import { Skeleton } from "@/shared/ui/skeleton"
import { Alert, AlertDescription } from "@/shared/ui/alert"
import { Calendar, Users, Phone, User } from "lucide-react"
import { useState } from "react"
import { Button } from "@/shared/ui/button"
import { ChevronDown, ChevronRight } from "lucide-react"

export default function DailyVotesPage() {
  const { dailyData, totalVotes, totalDays, isLoading, isError, error, refetch } = useDailyVotes()
  const [expandedDays, setExpandedDays] = useState<Set<string>>(new Set())

  const toggleDay = (date: string) => {
    const newExpanded = new Set(expandedDays)
    if (newExpanded.has(date)) {
      newExpanded.delete(date)
    } else {
      newExpanded.add(date)
    }
    setExpandedDays(newExpanded)
  }

  if (isLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Kunlar bo'yicha ovozlar</h1>
            <p className="text-muted-foreground">Ovozlarning kunlik statistikasi</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-6 w-16" />
              </CardHeader>
            </Card>
          ))}
        </div>

        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className="container mx-auto p-6">
        <Alert variant="destructive">
          <AlertDescription>
            Kunlik ovozlarni yuklashda xatolik yuz berdi. 
            <Button variant="link" onClick={() => refetch()} className="p-0 ml-2">
              Qayta urinish
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Kunlar bo'yicha ovozlar</h1>
          <p className="text-muted-foreground">Ovozlarning kunlik statistikasi</p>
        </div>
        <Button onClick={() => refetch()} variant="outline">
          Yangilash
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Jami ovozlar</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalVotes}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Jami kunlar</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalDays}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">O'rtacha kunlik</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {totalDays > 0 ? Math.round(totalVotes / totalDays) : 0}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Daily Data */}
      <div className="space-y-4">
        {dailyData.length === 0 ? (
          <Card>
            <CardContent className="p-6">
              <p className="text-center text-muted-foreground">Hech qanday ovoz topilmadi</p>
            </CardContent>
          </Card>
        ) : (
          dailyData.map((dayData) => (
            <Card key={dayData.date}>
              <CardHeader
                className="cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={() => toggleDay(dayData.date)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      {expandedDays.has(dayData.date) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                      <Calendar className="h-5 w-5 text-muted-foreground" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">
                        {dayData.date === 'Без даты' ? 'Sana ko\'rsatilmagan' : dayData.date}
                      </CardTitle>
                      <CardDescription>
                        {dayData.count} ta ovoz
                      </CardDescription>
                    </div>
                  </div>
                  <Badge variant="secondary" className="text-sm">
                    {dayData.count}
                  </Badge>
                </div>
              </CardHeader>

              {expandedDays.has(dayData.date) && (
                <CardContent className="pt-0">
                  <div className="grid gap-3">
                    {dayData.votes.map((vote) => (
                      <div
                        key={vote._id}
                        className="flex items-center justify-between p-3 border rounded-lg bg-muted/20"
                      >
                        <div className="flex items-center space-x-3">
                          <Phone className="h-4 w-4 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{vote.originalPhoneNumber}</p>
                            {vote.phoneNumber && (
                              <p className="text-sm text-muted-foreground">
                                Telefon: {vote.phoneNumber}
                              </p>
                            )}
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          {vote.owner && (
                            <div className="flex items-center space-x-1">
                              <User className="h-4 w-4 text-muted-foreground" />
                              <span className="text-sm">{vote.owner.name}</span>
                            </div>
                          )}
                          <Badge variant={vote.ownerId ? "default" : "outline"}>
                            {vote.ownerId ? "Biriktirilgan" : "Biriktirilmagan"}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          ))
        )}
      </div>
    </div>
  )
}
