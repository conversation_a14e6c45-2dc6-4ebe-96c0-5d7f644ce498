'use server';

import { dbConnect } from "@/shared/lib/database";
import { VotesSchema } from "@/entity/votes/schema";

export type DailyVoteData = {
    date: string;
    count: number;
    votes: {
        _id: string;
        originalPhoneNumber: string;
        voteDate: string;
        phoneNumber?: string;
        ownerId?: string;
        owner?: { _id: string; name: string };
        updatedBy?: string;
        createdAt: string;
        updatedAt: string;
    }[];
};

export type DailyVotesResult = {
    dailyData: DailyVoteData[];
    totalVotes: number;
    totalDays: number;
};

/**
 * Получает голоса, сгруппированные по дням
 */
export async function getDailyVotes(): Promise<DailyVotesResult> {
    await dbConnect();

    try {
        // Агрегация для группировки голосов по дням
        const aggregationPipeline = [
            {
                $lookup: {
                    from: 'villagepeople',
                    localField: 'ownerId',
                    foreignField: '_id',
                    as: 'ownerInfo'
                }
            },
            {
                $group: {
                    _id: "$voteDate",
                    count: { $sum: 1 },
                    votes: {
                        $push: {
                            _id: "$_id",
                            originalPhoneNumber: "$originalPhoneNumber",
                            voteDate: "$voteDate",
                            phoneNumber: "$phoneNumber",
                            ownerId: "$ownerId",
                            ownerInfo: "$ownerInfo",
                            updatedBy: "$updatedBy",
                            createdAt: "$createdAt",
                            updatedAt: "$updatedAt"
                        }
                    }
                }
            },
            {
                $sort: { _id: -1 } // Сортировка по дате (новые сверху)
            }
        ];

        const results = await VotesSchema.aggregate(aggregationPipeline);

        // Преобразуем результаты в нужный формат
        const dailyData: DailyVoteData[] = results.map((result) => ({
            date: result._id || 'Без даты',
            count: result.count,
            votes: result.votes.map((vote: any) => ({
                _id: vote._id.toString(),
                originalPhoneNumber: vote.originalPhoneNumber,
                voteDate: vote.voteDate || '',
                phoneNumber: vote.phoneNumber || undefined,
                ownerId: vote.ownerId ? vote.ownerId.toString() : undefined,
                owner: vote.ownerInfo && vote.ownerInfo.length > 0 ? {
                    _id: vote.ownerInfo[0]._id.toString(),
                    name: vote.ownerInfo[0].name
                } : undefined,
                updatedBy: vote.updatedBy || undefined,
                createdAt: vote.createdAt.toISOString(),
                updatedAt: vote.updatedAt.toISOString(),
            }))
        }));

        // Подсчитываем общую статистику
        const totalVotes = dailyData.reduce((sum, day) => sum + day.count, 0);
        const totalDays = dailyData.length;

        return {
            dailyData,
            totalVotes,
            totalDays
        };

    } catch (error) {
        console.error('Error fetching daily votes:', error);
        throw new Error('Kunlik ovozlarni yuklashda xatolik yuz berdi');
    }
}
